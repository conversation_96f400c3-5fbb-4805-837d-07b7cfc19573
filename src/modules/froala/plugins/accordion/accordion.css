.fr-element.fr-view .accordion-panel {
    background: #f5f5f5;
  }
  .fr-element.fr-view .accordion-panel .accordion-header {
    color: #0067c7;
  }
  .fr-element.fr-view .accordion-panel {
    background: #f5f5f5;
  }
  .fr-element.fr-view .accordion-panel .accordion-header {
    color: #0067c7;
    padding: 8px 12px;
    background-color: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    /* Create the text field appearance */
    box-shadow:
      inset 1px 1px 2px rgba(0, 0, 0, 0.1),      /* Top-left inner shadow */
      inset -1px -1px 1px rgba(255, 255, 255, 0.8); /* Bottom-right inner highlight */
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    position: relative;
    outline: none;
  }

  .fr-element.fr-view .accordion-panel .accordion-header:hover {
    border-color: #9ca3af;
    box-shadow:
      inset 1px 1px 3px rgba(0, 0, 0, 0.15),     /* Slightly stronger shadow on hover */
      inset -1px -1px 1px rgba(255, 255, 255, 0.8);
  }

  .fr-element.fr-view .accordion-panel .accordion-header.fr-selected-cell {
    outline: 1px solid #3b82f6;
    outline-offset: -8px;
    box-shadow: inset 0px 0px 0px 7px rgba(0, 0, 0, 0.1), inset -1px -1px 1px rgba(255, 255, 255, 0.8), 0 0 0 2px rgba(59, 130, 246, 0.2);
  }

  .removeBtn{
    border: none;
    background: white;
    padding: 9px 4px 4px 4px;
    border-radius: 3px;
  }
  


